/* 基本樣式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 必填欄位星號 */
.required {
    color: #e74c3c;
    font-weight: bold;
    margin-left: 3px;
}

/* 水平表單排列 */
.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group-inline {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.form-group-inline label {
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-group-inline input {
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group-inline input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 響應式設計 - 小螢幕時垂直排列 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 15px;
    }
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    font-size: 21px;
    /* 字體放大50% (14px * 1.5 = 21px) */
}

.container {
    max-width: 1920px;
    /* 調整為32寸螢幕適用寬度 */
    margin: 0 auto;
    padding: 20px;
}

/* 標題 */
header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-logo {
    height: 50px;
    width: auto;
    border-radius: 8px;
}

.title-section {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

header h1 {
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
}

.subtitle {
    font-size: 0.9rem;
    font-weight: 400;
    opacity: 0.9;
    margin: 0;
    color: #f0f0f0;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    color: white;
}

/* 卡片樣式 */
.card {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card h2,
.card h3 {
    margin-bottom: 20px;
    color: #333;
}

/* 表單樣式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 24px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* 按鈕樣式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    font-size: 24px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* 導航標籤 */
.nav-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #eee;
}

.nav-tab {
    padding: 15px 25px;
    background: none;
    border: none;
    font-size: 24px;
    font-weight: 500;
    cursor: pointer;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s;
}

.nav-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.nav-tab:hover {
    color: #667eea;
}

/* 標籤內容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 搜尋列 */
.search-bar {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.search-bar input {
    flex: 1;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 24px;
}

/* 產品管理樣式 */
.product-header {
    margin-bottom: 25px;
}

.product-header h3 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
    font-size: 30px;
    font-weight: 600;
}

.search-controls {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.search-bar {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.search-bar label {
    font-weight: 600;
    color: #495057;
    min-width: 80px;
}

.search-bar input {
    flex: 1;
    min-width: 200px; /* 減少最小寬度 */
    padding: 10px 15px;
    border: 2px solid #dee2e6;
    border-radius: 5px;
    font-size: 21px;
}

.filter-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-options select {
    padding: 8px 12px;
    border: 2px solid #dee2e6;
    border-radius: 5px;
    background: white;
    font-size: 21px;
    min-width: 150px;
}

/* 產品表格樣式 */
.products-table-container {
    overflow-x: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
}

.products-table {
    width: 100%;
    border-collapse: collapse;
}

.products-table .no-data,
.products-table .error {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-style: italic;
}

.products-table .error {
    color: #dc3545;
}

.products-table .low-stock {
    color: #dc3545;
    font-weight: bold;
}

background: white;
font-size: 21px;
}

.products-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    font-weight: 600;
    padding: 12px 8px;
    text-align: center;
    border-bottom: 2px solid #dee2e6;
    border-right: 1px solid #dee2e6;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
}

.products-table th:last-child {
    border-right: none;
}

.products-table td {
    padding: 10px 8px;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    border-right: 1px solid #dee2e6;
    vertical-align: middle;
}

.products-table td:last-child {
    border-right: none;
}

.products-table tbody tr:hover {
    background-color: #f8f9fa;
}

.products-table tbody tr:nth-child(even) {
    background-color: #fdfdfd;
}

/* 產品狀態樣式 */
.product-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
    display: inline-block;
}

.product-status.in-stock {
    background: #d4edda;
    color: #155724;
}

.product-status.low-stock {
    background: #fff3cd;
    color: #856404;
}

.product-status.out-of-stock {
    background: #f8d7da;
    color: #721c24;
}

/* 操作按鈕樣式 */
.product-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.quantity-input {
    width: 50px;
    padding: 4px 6px;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    text-align: center;
    font-size: 18px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 18px;
    border-radius: 4px;
}

/* 分頁樣式 */
.table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-top: 1px solid #dee2e6;
    flex-wrap: wrap;
    gap: 15px;
}

.pagination-info {
    color: #6c757d;
    font-size: 21px;
}

/* 購物車數量輸入框樣式 */
.quantity-input-cart {
    width: 60px;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    margin: 0 8px;
    font-size: 21px;
    transition: border-color 0.3s;
}

.quantity-input-cart:focus {
    outline: none;
    border-color: #667eea;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-input-group {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 0 10px;
}

.page-input-group span {
    color: #6c757d;
    font-size: 21px;
}

#page-input {
    width: 60px;
    padding: 6px 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    text-align: center;
    font-size: 21px;
}

#page-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.page-number {
    padding: 6px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    text-decoration: none;
    border-radius: 4px;
    font-size: 21px;
    cursor: pointer;
    transition: all 0.2s;
}

.page-number:hover {
    background: #e9ecef;
}

.page-number.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* 產品詳細資訊樣式 */
.product-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
}



.product-name-cell {
    text-align: left;
    max-width: 200px;
    word-wrap: break-word;
}

.product-price {
    font-weight: 600;
    color: #28a745;
}

.stock-quantity {
    font-weight: 600;
}

.stock-low {
    color: #ffc107;
}

.stock-out {
    color: #dc3545;
}

/* 購物車樣式 */
.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    margin-bottom: 5px;
}

.cart-item-price {
    color: #667eea;
    font-weight: 500;
}

.cart-item-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.cart-summary {
    border-top: 2px solid #eee;
    padding-top: 20px;
    margin-top: 20px;
    text-align: right;
}

.total {
    font-size: 24px;
    margin-bottom: 20px;
    color: #333;
}

.cart-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* 訂單樣式 */
.order-item {
    border: 2px solid #eee;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    background: white;
}

.order-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

/* 對齊欄位：左=訂單編號 中=狀態 右=金額 */
.order-header .order-number {
    flex: 1;
    min-width: 0;
}

.order-header .order-status {
    width: 140px;
    display: flex;
    justify-content: center;
}

.order-header .order-total {
    width: 140px;
    text-align: right;
}

.order-number {
    font-weight: 600;
    color: #333;
}

.order-status {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 21px;
    font-weight: 500;
}

.order-status.pending {
    background: #fff3cd;
    color: #856404;
}

.order-status.confirmed {
    background: #d4edda;
    color: #155724;
}

.order-total {
    font-size: 18px;
    font-weight: 600;
    color: #667eea;
}

.order-notes {
    margin-top: 10px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 5px;
    font-size: 21px;
    color: #6c757d;
    border-left: 3px solid #dee2e6;
}

/* 載入指示器 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading p {
    color: white;
    font-size: 18px;
}

/* 訊息提示 */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 5px;
    font-weight: 500;
    z-index: 1001;
    animation: slideIn 0.3s ease-out;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 註冊連結 */
.register-link {
    text-align: center;
    margin-top: 20px;
}

.register-link a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.register-link a:hover {
    text-decoration: underline;
}

/* 產品網格樣式 舊版
.products-grid-container {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow-x: auto;
    margin-bottom: 20px;
    width: 100%;
}

.products-grid-header {
    display: grid;
    grid-template-columns: 180px 2fr 150px 100px 120px 150px 120px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    min-width: 1000px;
}
*/

.grid-header-item {
    padding: 12px 8px;
    font-weight: 600;
    color: #495057;
    text-align: center;
    border-right: 1px solid #dee2e6;
    white-space: nowrap;
}

.grid-header-item:last-child {
    border-right: none;
}

.products-grid-body {
    background: white;
}
/*
.product-card {
    display: grid;
    grid-template-columns: 180px 2fr 150px 100px 120px 150px 120px;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.2s;
    min-width: 1000px;
}
*/
.product-card:hover {
    background-color: #f8f9fa;
}

.product-card:last-child {
    border-bottom: none;
}

.product-card > div {
    padding: 12px 8px;
    border-right: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.product-card > div:last-child {
    border-right: none;
}

.product-name {
    text-align: left !important;
    justify-content: flex-start !important;
    flex-direction: column;
    align-items: flex-start !important;
}

.product-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.product-ingredients {
    font-size: 18px;
    color: #666;
}

.nhi-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.nhi-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
}

.nhi-level {
    font-size: 18px;
    color: #28a745;
}

.price-value {
    font-weight: 600;
    color: #28a745;
    font-size: 20px;
}

.price-unit {
    font-size: 16px;
    color: #666;
}

.quantity-input {
    width: 50px;
    padding: 4px;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    text-align: center;
    font-size: 16px;
}

.quantity-label {
    font-size: 14px;
    color: #666;
    margin-top: 2px;
}

.action-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s;
    margin: 0 2px;
}

.action-btn.cart {
    color: #667eea;
}

.action-btn.favorite {
    color: #e74c3c;
}

.action-btn:hover {
    background-color: rgba(0,0,0,0.1);
    transform: scale(1.1);
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
}

.status-badge.in-stock {
    background: #d4edda;
    color: #155724;
}

.status-badge.low-stock {
    background: #fff3cd;
    color: #856404;
}

.status-badge.out-of-stock {
    background: #f8d7da;
    color: #721c24;
}

/* 響應式設計 */
@media (max-width: px) {
    .container {
        padding: 10px;
    }

    header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .nav-tabs {
        flex-wrap: wrap;
    }

    .nav-tab {
        flex: 1;
        min-width: 100px; /* 減少最小寬度 */
        font-size: 16px; /* 稍微縮小字體 */
        padding: 8px 4px; /* 調整內距 */
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    /* 產品網格響應式設計 */
    .products-grid-header {
        display: none; /* 隱藏桌面版標題 */
    }

    .product-card {
        display: block; /* 改為塊狀佈局 */
        border: 1px solid #dee2e6;
        border-radius: 8px;
        margin-bottom: 15px;
        padding: 15px;
    }

    .product-card > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-right: none;
        border-bottom: 1px solid #eee;
        text-align: left;
    }

    .product-card > div:last-child {
        border-bottom: none;
    }

    .product-card > div::before {
        content: attr(data-label) ": ";
        font-weight: 600;
        color: #495057;
        min-width: 80px;
    }

    .product-name::before {
        content: "品名: ";
    }

    .nhi-info::before {
        content: "健保: ";
    }

    .product-dosage::before {
        content: "規格: ";
    }

    .product-price::before {
        content: "單價: ";
    }

    .product-quantity::before {
        content: "數量: ";
    }

    .product-actions::before {
        content: "功能: ";
    }

    .product-status::before {
        content: "狀態: ";
    }

    .cart-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .cart-item-actions {
        width: 100%;
        justify-content: space-between;
    }

    .search-bar {
        flex-direction: column;
        gap: 10px;
    }
    
    .search-bar input {
        min-width: auto; /* 移除最小寬度限制 */
        width: 100%;
    }
    
    .search-controls {
        flex-direction: column;
        gap: 15px;
    }
}

/* 系統
管理樣式 */
.admin-nav {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.admin-nav-tab {
    padding: 8px 16px;
    border: none;
    background: none;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.admin-nav-tab.active {
    border-bottom-color: #007bff;
    color: #007bff;
    font-weight: bold;
}

.admin-nav-tab:hover {
    background-color: #f8f9fa;
}

.admin-tab-content {
    display: none;
}

.admin-tab-content.active {
    display: block;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.admin-table th,
.admin-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.admin-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.admin-table tr:hover {
    background-color: #f5f5f5;
}

.role-actions {
    display: flex;
    gap: 5px;
}

.role-actions .btn {
    padding: 4px 8px;
    font-size: 18px;
}

/* 權限列表樣式 */
.permissions-group {
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
}

.permissions-group-header {
    background-color: #f8f9fa;
    padding: 10px 15px;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
}

.permissions-group-content {
    padding: 15px;
}

.permission-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.permission-item:last-child {
    border-bottom: none;
}

.permission-name {
    font-weight: bold;
}

.permission-description {
    color: #666;
    font-size: 21px;
}

.permission-action {
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 18px;
}

/* 模態框樣式 */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 900px;
    /* 增加模態框寬度 */
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ddd;
}

.modal-header h3 {
    margin: 0;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
}

.close:hover {
    color: #000;
}

.modal-body {
    padding: 20px;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* 權限複選框網格 */
.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 15px;
    border-radius: 5px;
}

.permission-checkbox-group {
    border: 1px solid #eee;
    border-radius: 5px;
    padding: 10px;
}

.permission-checkbox-group h5 {
    margin: 0 0 10px 0;
    color: #007bff;
    font-size: 21px;
    font-weight: bold;
}

.permission-checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.permission-checkbox input {
    margin-right: 8px;
}

.permission-checkbox label {
    font-size: 13px;
    cursor: pointer;
}

/* 權限列表樣式 */
.permission-group {
    margin-bottom: 25px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.permission-group h4 {
    background-color: #f5f5f5;
    margin: 0;
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    color: #333;
    font-size: 24px;
}

.permission-items {
    padding: 15px;
}

.permission-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;
    padding: 10px;
    background-color: #fafafa;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}

.permission-item:last-child {
    margin-bottom: 0;
}

.permission-item strong {
    color: #333;
    font-size: 21px;
    margin-bottom: 4px;
}

.permission-item span {
    color: #666;
    font-size: 13px;
}

/* 用戶搜尋 */
.user-search {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.user-search input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* 用戶權限卡片 */
.user-permission-card {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
}

.user-permission-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.user-name {
    font-weight: bold;
    font-size: 24px;
}

.user-role {
    background-color: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 18px;
}

.user-permissions {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.user-permission-tag {
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .admin-nav {
        flex-direction: column;
    }

    .admin-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .permissions-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .user-permission-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

/* 購物車圖標按鈕樣式 */
.btn-cart-icon {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s;
    color: #667eea;
}

.btn-cart-icon:hover {
    background-color: rgba(102, 126, 234, 0.1);
    transform: scale(1.1);
}

.btn-cart-icon:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    color: #ccc;
}

.btn-cart-icon:disabled:hover {
    background: none;
    transform: none;
}

/* 訂單詳情模態框樣式 */
.order-detail-modal .modal-content {
    max-width: 1200px;
    /* 增加訂單詳情寬度 */
}

.order-detail-header {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.order-detail-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 15px;
}

.order-detail-info>div {
    display: flex;
    flex-direction: column;
}

.order-detail-info label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    font-size: 21px;
}

.order-detail-info span {
    color: #333;
    font-size: 24px;
}

.order-detail-info select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    background-color: #fff;
    color: #495057;
    cursor: pointer;
}

.order-detail-info select:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.order-detail-info select:hover {
    border-color: #adb5bd;
}

.order-items-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.order-items-table th,
.order-items-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.order-items-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    color: #495057;
}

.order-items-table tbody tr:hover {
    background-color: #f8f9fa;
}

.order-items-table .product-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
}

.order-items-table .price {
    text-align: right;
    font-weight: 600;
    color: #28a745;
}

.order-items-table .quantity {
    text-align: center;
    font-weight: 600;
}

.order-items-table .stock-status {
    text-align: center;
    padding: 8px 12px;
}

.order-items-table .stock-status input[type="checkbox"] {
    margin-right: 5px;
    transform: scale(1.2);
}

.order-items-table .stock-status label {
    font-size: 12px;
    color: #6c757d;
    cursor: pointer;
}

.order-items-table .stock-status input[type="checkbox"]:checked+label {
    color: #dc3545;
    font-weight: 600;
}

.text-danger {
    color: #dc3545;
    font-weight: 600;
}

.text-muted {
    color: #6c757d;
}

.order-total-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
    text-align: right;
}

.order-total-amount {
    font-size: 30px;
    font-weight: 700;
    color: #667eea;
    margin-top: 10px;
}

/* 左右分佈的按鈕佈局 */
.order-actions-spread {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 放大版更新按鈕（靠左） */
.update-btn-large {
    background: #28a745;
    color: white;
    border: none;
    padding: 18px 36px;
    border-radius: 8px;
    font-size: 24px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    min-width: 200px;
    justify-content: center;
}

.update-btn-large:hover {
    background: #218838;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
}

.update-btn-large:active {
    transform: translateY(0);
    box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3);
}

.update-btn-large i {
    font-size: 20px;
}

/* 放大版列印按鈕（靠右） */
.print-btn-large {
    background: #6c757d;
    color: white;
    border: none;
    padding: 18px 36px;
    border-radius: 8px;
    font-size: 24px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    min-width: 200px;
    justify-content: center;
}

.print-btn-large:hover {
    background: #545b62;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(108, 117, 125, 0.3);
}

.print-btn-large:active {
    transform: translateY(0);
    box-shadow: 0 3px 6px rgba(108, 117, 125, 0.3);
}

.print-btn-large i {
    font-size: 20px;
}

/* 訂單操作按鈕區域 */
.order-actions {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.order-actions .update-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    min-width: 140px;
    justify-content: center;
}

.order-actions .update-btn:hover {
    background: #218838;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.order-actions .update-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.order-actions .update-btn i {
    font-size: 14px;
}

/* 列印按鈕樣式 */
.order-actions .print-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    min-width: 140px;
    justify-content: center;
    margin-left: 10px;
}

.order-actions .print-btn:hover {
    background: #545b62;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

.order-actions .print-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

.order-actions .print-btn i {
    font-size: 14px;
}

/* 放大狀態選擇器 */
.enlarged-status-select {
    font-size: 1.5em !important;
    /* 放大50% */
    padding: 8px 12px !important;
    min-width: 150px !important;
    margin-right: 15px !important;
    border: 2px solid #667eea !important;
    border-radius: 6px !important;
    background-color: white !important;
    font-weight: 500 !important;
}

.order-status-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 21px;
    font-weight: 500;
    text-align: center;
}

.order-status-badge.pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.order-status-badge.confirmed {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.order-status-badge.processing {
    background: #cce5f6;
    color: #004085;
    border: 1px solid #b3d7ff;
}

.order-status-badge.shipped {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.order-status-badge.delivered {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.order-status-badge.cancelled {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.order-number-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    transition: color 0.2s;
}

.order-number-link:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .order-detail-modal .modal-content {
        width: 95%;
        max-width: none;
    }

    .order-detail-info {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .order-items-table {
        font-size: 21px;
    }

    .order-items-table th,
    .order-items-table td {
        padding: 8px 4px;
    }
}

/* 管理員訂單管理樣式 */
.orders-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.admin-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
    min-width: 300px;
}

.order-filters {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.order-filters select,
.order-filters input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 21px;
}

.order-filters select {
    min-width: 120px;
}



.order-stats {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.stat-item {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 6px;
    text-align: center;
    min-width: 80px;
}

.stat-item .stat-number {
    font-size: 18px;
    font-weight: bold;
    color: #007bff;
    display: block;
}

.stat-item .stat-label {
    font-size: 18px;
    color: #666;
    margin-top: 2px;
}

/* 管理員訂單項目樣式 */
.admin-order-item {
    border-left: 4px solid #007bff;
    background: #f8f9fa;
}

.admin-order-item .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.admin-order-actions {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.admin-order-actions .btn {
    padding: 4px 8px;
    font-size: 18px;
    border-radius: 3px;
}

.order-user {
    background: #e3f2fd;
    padding: 8px 12px;
    border-radius: 4px;
    margin: 8px 0;
    font-size: 21px;
}

.order-user strong {
    color: #1976d2;
}

.order-item .status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 14px;
    border-radius: 16px;
    font-size: 20px;
    /* 放大一倍 */
    font-weight: 700;
    line-height: 1;
    min-width: 96px;
    /* 固定寬度確保對齊 */
    text-transform: none;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-confirmed {
    background: #d4edda;
    color: #155724;
}

.status-processing {
    background: #cce5ff;
    color: #004085;
}

.status-shipped {
    background: #e2e3e5;
    color: #383d41;
}

.status-delivered {
    background: #d1ecf1;
    color: #0c5460;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .orders-header {
        flex-direction: column;
        align-items: stretch;
    }

    .admin-controls {
        min-width: auto;
    }

    .order-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .order-filters select {
        width: 100%;
    }

    .order-stats {
        justify-content: space-between;
    }

    .stat-item {
        flex: 1;
        min-width: 60px;
    }
}

/* 批次操作樣式 
*/
.batch-operations {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.batch-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.selected-count {
    font-size: 21px;
    color: #666;
    font-weight: bold;
}

.order-checkbox {
    margin-right: 10px;
    transform: scale(1.2);
}

.order-item.selected {
    background: #e3f2fd;
    border-left-color: #2196f3;
}

.order-item {
    position: relative;
    cursor: pointer;
    transition: background-color 0.2s;
}

.order-item:hover {
    background: #f5f5f5;
}

.order-item.selected:hover {
    background: #e1f5fe;
}

.order-actions {
    display: flex;
    gap: 5px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.order-actions .btn {
    padding: 3px 8px;
    font-size: 11px;
    border-radius: 3px;
}

.btn-status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.btn-status-processing {
    background: #cce5ff;
    color: #004085;
    border: 1px solid #74b9ff;
}

.btn-status-shipped {
    background: #d4edda;
    color: #155724;
    border: 1px solid #00b894;
}

.btn-status-pending:hover {
    background: #ffeaa7;
}

.btn-status-processing:hover {
    background: #74b9ff;
    color: white;
}

.btn-status-shipped:hover {
    background: #00b894;
    color: white;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .batch-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .batch-controls select,
    .batch-controls button {
        width: 100%;
    }

    .order-actions {
        justify-content: center;
    }
}

/* 大螢幕優化 (針對 27 吋螢幕等) */
@media (min-width: 1920px) {

    .container {
        max-width: 2200px;
        padding: 30px 40px;
    }

    /* 產品表格優化 */
    .product-table th,
    .product-table td {
        padding: 15px 20px;
        font-size: 24px;
    }

    .product-table th:first-child,
    .product-table td:first-child {
        min-width: 120px;
        /* 產品代碼欄位 */
    }

    .product-table th:nth-child(2),
    .product-table td:nth-child(2) {
        min-width: 400px;
        /* 產品名稱欄位 */
    }

    /* 搜尋區域 */
    .search-section {
        padding: 30px;
        margin-bottom: 30px;
    }

    .search-controls {
        gap: 20px;
    }

    /* 模態框 */
    .modal-content {
        max-width: 1200px;
    }

    .order-detail-modal .modal-content {
        max-width: 1500px;
    }

    /* 卡片佈局更寬
    .product-card {
        padding: 25px;
    }
         */

    /* 按鈕更大 */
    .btn {
        padding: 12px 24px;
        font-size: 24px;
    }

    /* 標題區域 */
    header {
        padding: 30px;
        margin-bottom: 40px;
    }

    .header-logo {
        height: 60px;
    }
}

/* 產品網格卡片佈局 */
.products-grid-container {
    width: 100%;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    /* 讓容器本身成為可滾動區，避免表頭與內容因捲軸寬度不一致而錯位 */
    max-height: 600px;
    overflow-y: auto;
    scrollbar-gutter: stable;
    /* 統一欄位寬度設定（預設） */
    --grid-columns: 180px 320px 120px 170px 170px 170px 170px;
}

.products-grid-header {
    display: grid;
    grid-template-columns: var(--grid-columns);
    background: #4a9eff;
    color: white;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
    /* 固定在容器頂部 */
    position: sticky;
    top: 0;
    z-index: 2;
}

.grid-header-item {
    padding: 20px 12px;
    text-align: center;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.grid-header-item:last-child {
    border-right: none;
}

.products-grid-body {
    /* 滾動交由容器處理，避免表頭與內容寬度不一致 */
}

/* 產品卡片 */
.product-card {
    display: grid;
    grid-template-columns: var(--grid-columns);
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
    background: white;
    min-height: 80px;
    align-items: center;
}

.product-card:hover {
    background: #f8f9ff;
}

.product-card:last-child {
    border-bottom: none;
}

/* 健保資訊欄 */
.nhi-info {
    padding: 20px 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    border-right: 1px solid #f0f0f0;
    height: 100%;
}

.drug-icon {
    width: 40px;
    height: 40px;
    background: #e3f2fd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    color: #1976d2;
}

.nhi-details {
    flex: 1;
}

.nhi-code {
    font-weight: bold;
    color: #333;
    font-size: 18px;
    margin-bottom: 2px;
}

.nhi-level {
    font-size: 11px;
    color: #666;
}

.nhi-expiry {
    font-size: 10px;
    color: #999;
    margin-top: 2px;
}

/* 產品名稱欄 */
.product-name {
    padding: 20px 12px;
    border-right: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
}

.product-dosage {
    padding: 20px 12px;
    border-right: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    text-align: center;
}

.dosage-form {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.product-title {
    font-weight: bold;
    color: #333;
    font-size: 21px;
    margin-bottom: 4px;
}

.product-ingredients {
    font-size: 18px;
    color: #666;
}

/* 價格欄 */
.product-price {
    padding: 20px 12px;
    text-align: center;
    border-right: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.price-value {
    font-weight: bold;
    color: #333;
    font-size: 24px;
}

.price-unit {
    font-size: 11px;
    color: #666;
}

/* 數量欄 */
.product-quantity {
    padding: 20px 12px;
    text-align: center;
    border-right: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.quantity-input {
    width: 60px;
    padding: 5px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 18px;
}

.quantity-label {
    font-size: 10px;
    color: #666;
    margin-top: 2px;
}

/* 功能欄 */
.product-actions {
    padding: 20px 12px;
    text-align: center;
    border-right: 1px solid #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
}

.action-btn {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    transition: all 0.2s ease;
}

.action-btn.download {
    background: #e8f5e8;
    color: #4caf50;
}

.action-btn.favorite {
    background: #fff3e0;
    color: #ff9800;
}

.action-btn.cart {
    background: #e8f5e8;
    color: #4caf50;
    font-size: 16px;
}

.action-btn.cart:disabled {
    background: #f5f5f5;
    color: #ccc;
    cursor: not-allowed;
    transform: none;
}

.action-btn.refresh {
    background: #e3f2fd;
    color: #2196f3;
}

.action-btn:hover {
    transform: scale(1.1);
}

/* 狀態欄 */
.product-status {
    padding: 20px 12px;
    text-align: center;
    border-right: 1px solid #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.product-status .status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 14px;
    border-radius: 16px;
    font-size: 20px;
    /* 放大一倍 */
    font-weight: 700;
    line-height: 1;
    text-transform: none;
    min-width: 96px;
    /* 統一寬度，避免看起來不對齊 */
}

.status-badge.in-stock {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-badge.supply-interrupted {
    background: #fff3e0;
    color: #f57c00;
}

.status-badge.out-of-stock {
    background: #ffebee;
    color: #d32f2f;
}

/* 保存欄 */
.product-save {
    padding: 20px 12px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.save-btn {
    background: #4a9eff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: background-color 0.2s ease;
}

.save-btn:hover {
    background: #357abd;
}

/* 響應式設計 */
@media (max-width: 1400px) {
    .products-grid-container {
        --grid-columns: 160px 500px 180px 200px 200px 200px 80px;
    }
}

@media (max-width: 1200px) {
    .products-grid-container {
        --grid-columns: 140px 450px 160px 200px 200px 200px 70px;
    }

    .drug-icon {
        width: 35px;
        height: 35px;
        font-size: 18px;
    }
}

@media (max-width: 768px) {
    .products-grid-container {
        overflow-x: auto;
        --grid-columns: 120px 180px 70px 150px 120px 120px 50px;
    }

    .products-grid-header,
    .product-card {
        min-width: 810px; /* 減少最小寬度，讓內容更適合小螢幕 */
    }
    
    /* 在極小螢幕上改用卡片式佈局 */
    @media (max-width: 480px) {
        .products-grid-container {
            display: block;
        }
        
        .products-grid-header {
            display: none; /* 隱藏表格標題 */
        }
        
        .product-card {
            display: block;
            min-width: auto;
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        
        .product-card > div {
            display: block;
            margin-bottom: 8px;
            padding: 0;
        }
        
        .product-card > div:before {
            content: attr(data-label) ": ";
            font-weight: bold;
            color: #666;
        }
    }
}

/* 訊息管理樣式 - 美化版 */

/* 頁面標題區域 */
.message-compose-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    color: white;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.header-icon {
    font-size: 48px;
    opacity: 0.9;
}

.header-text h3 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
}

.header-text p {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
}

/* 表單容器 */
.message-form-wrapper {
    max-width: 900px;
    margin: 0 auto;
}

.message-form {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

/* 表單區塊 */
.form-section {
    padding: 32px;
    border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
    border-bottom: none;
}

.section-header {
    margin-bottom: 24px;
}

.section-header h4 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-icon {
    font-size: 20px;
}

/* 表單標籤 */
.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
}

.label-text {
    margin-right: 4px;
}

.required {
    color: #e53e3e;
    font-weight: bold;
}

.optional {
    color: #718096;
    font-weight: normal;
    font-size: 12px;
}

/* 表單控件美化 */
.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-enhanced {
    font-size: 16px;
    padding: 14px 16px;
}

.select-enhanced {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.textarea-enhanced {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
    line-height: 1.6;
}

.textarea-wrapper {
    position: relative;
}

.char-counter {
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 12px;
    color: #718096;
    background: rgba(255, 255, 255, 0.9);
    padding: 2px 6px;
    border-radius: 4px;
}

.input-hint {
    margin-top: 6px;
    font-size: 12px;
    color: #718096;
}

/* 發送方式卡片 */
.send-methods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 12px;
}

.method-card {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
    position: relative;
    display: block;
}

.method-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.method-card input[type="checkbox"] {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 18px;
    height: 18px;
    accent-color: #667eea;
}

.method-card input[type="checkbox"]:checked+.method-content {
    color: #667eea;
}

.method-card input[type="checkbox"]:checked {
    background: #667eea;
}

.method-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding-right: 30px;
}

.method-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f7fafc;
    border-radius: 8px;
}

.method-info {
    flex: 1;
}

.method-name {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 2px;
}

.method-desc {
    font-size: 12px;
    color: #718096;
}

/* 日期時間選擇器 */
.datetime-wrapper {
    position: relative;
}

.datetime-enhanced {
    padding: 14px 16px;
    font-size: 14px;
}

.datetime-hint {
    margin-top: 8px;
    padding: 12px;
    background: #f7fafc;
    border-radius: 6px;
    font-size: 13px;
    color: #4a5568;
    display: flex;
    align-items: center;
    gap: 8px;
}

.hint-icon {
    font-size: 16px;
}

/* 操作按鈕區域 */
.form-actions-enhanced {
    padding: 24px 32px;
    background: #f8f9fa;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-outline {
    background: white;
    color: #4a5568;
    border: 2px solid #e2e8f0;
}

.btn-outline:hover {
    border-color: #cbd5e0;
    background: #f7fafc;
}

.btn-primary-enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary-enhanced:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.btn-icon {
    font-size: 16px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .message-compose-header {
        padding: 20px;
        margin-bottom: 20px;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .header-icon {
        font-size: 36px;
    }

    .header-text h3 {
        font-size: 24px;
    }

    .form-section {
        padding: 24px 20px;
    }

    .send-methods-grid {
        grid-template-columns: 1fr;
    }

    .form-actions-enhanced {
        padding: 20px;
        flex-direction: column;
    }

    .btn {
        justify-content: center;
    }
}

.checkbox-group {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.filter-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 20px;
}

.filter-controls select,
.filter-controls input {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.message-history-container {
    overflow-x: auto;
}

.message-type-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.message-type-badge.promotion {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.message-type-badge.announcement {
    background-color: #e3f2fd;
    color: #1565c0;
}

.message-type-badge.maintenance {
    background-color: #fff3e0;
    color: #ef6c00;
}

.message-type-badge.urgent {
    background-color: #ffebee;
    color: #c62828;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.status-badge.sent {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status-badge.pending {
    background-color: #fff3e0;
    color: #ef6c00;
}

.status-badge.failed {
    background-color: #ffebee;
    color: #c62828;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.template-card {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.template-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.template-card:hover {
    border-color: #667eea;
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.template-card:hover::before {
    transform: scaleX(1);
}

.template-card:active {
    transform: translateY(-2px);
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.template-header h5 {
    margin: 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
}

.template-icon {
    font-size: 18px;
}

.template-type-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.template-type-badge.promotion {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.template-type-badge.announcement {
    background-color: #e3f2fd;
    color: #1565c0;
}

.template-type-badge.maintenance {
    background-color: #fff3e0;
    color: #ef6c00;
}

.template-type-badge.urgent {
    background-color: #ffebee;
    color: #c62828;
}

.template-content h6 {
    margin: 0 0 8px 0;
    color: #555;
    font-size: 14px;
}

.template-content p {
    margin: 0;
    color: #666;
    font-size: 13px;
    line-height: 1.4;
}

.template-actions {
    display: flex;
    gap: 10px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
}

.template-actions .btn {
    padding: 8px 16px;
    font-size: 13px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.template-actions .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.template-actions .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.template-actions .btn-secondary {
    background: white;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.template-actions .btn-secondary:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

.template-actions .btn-danger {
    background: white;
    color: #e53e3e;
    border: 1px solid #fed7d7;
}

.template-actions .btn-danger:hover {
    background: #fed7d7;
    border-color: #feb2b2;
}

.message-preview {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    background: white;
}

.preview-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
}

.preview-header h4 {
    margin: 0;
    color: #333;
}

.preview-content {
    margin-bottom: 16px;
    line-height: 1.6;
}

.preview-footer {
    padding-top: 12px;
    border-top: 1px solid #eee;
    color: #666;
    font-size: 14px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .checkbox-group {
        flex-direction: column;
        gap: 10px;
    }

    .form-actions {
        flex-direction: column;
    }

    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .templates-grid {
        grid-template-columns: 1fr;
    }

    .template-actions {
        flex-direction: column;
    }
}

/* 訊息詳
情樣式 */
.message-detail {
    padding: 20px;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.detail-header h4 {
    margin: 0;
    color: #333;
}

.detail-info {
    margin-bottom: 20px;
}

.detail-info p {
    margin: 8px 0;
    color: #666;
}

.detail-content h5 {
    margin: 0 0 10px 0;
    color: #333;
}

.message-content-display {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    line-height: 1.6;
    color: #495057;
    min-height: 100px;
}

/*
 動畫效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.message-form-wrapper {
    animation: slideInUp 0.6s ease-out;
}

.message-compose-header {
    animation: fadeIn 0.8s ease-out;
}

.form-section {
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.form-section:nth-child(1) {
    animation-delay: 0.1s;
}

.form-section:nth-child(2) {
    animation-delay: 0.2s;
}

.form-section:nth-child(3) {
    animation-delay: 0.3s;
}

/* 表單驗證樣式 */
.form-control.error {
    border-color: #e53e3e;
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.form-control.success {
    border-color: #38a169;
    box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
}

.error-message {
    color: #e53e3e;
    font-size: 12px;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.success-message {
    color: #38a169;
    font-size: 12px;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 載入狀態 */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #2d3748;
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 改善選擇框樣式 */
.method-card input[type="checkbox"]:checked+.method-content {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 8px;
    padding: 4px;
    margin: -4px;
}

.method-card input[type="checkbox"]:checked {
    transform: scale(1.1);
}

/* 表單區塊間距調整 */
.form-group {
    margin-bottom: 24px;
}

.form-group:last-child {
    margin-bottom: 0;
}

/* 改善焦點狀態 */
.form-control:focus {
    transform: translateY(-1px);
}

.method-card:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 成功狀態動畫 */
@keyframes successPulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

.form-control.success {
    animation: successPulse 0.3s ease-out;
}

/ * 範本頁面標題樣式 */ .templates-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    color: white;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.templates-header .header-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.templates-header .header-icon {
    font-size: 48px;
    opacity: 0.9;
}

.templates-header .header-text h3 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
}

.templates-header .header-text p {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
}

/* 使用指南樣式 */
.templates-usage-guide {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    border-left: 4px solid #667eea;
}

.guide-content h4 {
    margin: 0 0 16px 0;
    color: #2d3748;
    font-size: 18px;
    font-weight: 600;
}

.guide-steps {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.step {
    display: flex;
    align-items: center;
    gap: 12px;
}

.step-number {
    width: 28px;
    height: 28px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    flex-shrink: 0;
}

.step-text {
    color: #4a5568;
    font-size: 14px;
    line-height: 1.5;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .templates-header {
        flex-direction: column;
        text-align: center;
    }

    .templates-header .header-content {
        flex-direction: column;
        text-align: center;
    }

    .templates-header .header-icon {
        font-size: 36px;
    }

    .templates-header .header-text h3 {
        font-size: 24px;
    }

    .guide-steps {
        gap: 16px;
    }

    .step {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
}

/*
 促銷訊息樣式（一般用戶查看） */
.no-messages {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-messages-icon {
    font-size: 64px;
    margin-bottom: 16px;
}

.no-messages h3 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 24px;
}

.no-messages p {
    margin: 0;
    font-size: 16px;
}

.promotion-message-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.promotion-message-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.promotion-message-card[data-type="promotion"] {
    border-left-color: #2e7d32;
}

.promotion-message-card[data-type="announcement"] {
    border-left-color: #1565c0;
}

.promotion-message-card[data-type="maintenance"] {
    border-left-color: #ef6c00;
}

.promotion-message-card[data-type="urgent"] {
    border-left-color: #c62828;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.message-type-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.message-icon {
    font-size: 20px;
}

.message-type-label {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.message-date {
    color: #666;
    font-size: 14px;
}

.message-content {
    margin-bottom: 20px;
}

.message-title {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.3;
}

.message-text {
    color: #555;
    font-size: 16px;
    line-height: 1.6;
}

.message-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
}

.message-methods {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.method-label {
    color: #666;
    font-size: 14px;
}

.method-tag {
    background: #f8f9fa;
    color: #495057;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .promotion-message-card {
        padding: 16px;
        margin-bottom: 16px;
    }

    .message-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .message-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .message-title {
        font-size: 18px;
    }

    .message-text {
        font-size: 14px;
    }
}

/* ====
================ 用戶審核管理樣式 ==================== */

.approval-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 25px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border-left: 4px solid #007bff;
}

.approval-header .header-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.approval-header .header-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.approval-header .header-text h3 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
}

.approval-header .header-text p {
    margin: 0;
    color: #666;
    font-size: 16px;
}

.approval-stats {
    display: flex;
    gap: 20px;
}

.stat-card {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-width: 80px;
}

.stat-card .stat-number {
    font-size: 28px;
    font-weight: bold;
    color: #007bff;
    display: block;
    line-height: 1;
}

.stat-card .stat-label {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

/* 黑名單統計卡特殊樣式 */
.stat-card.rejected {
    background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
    color: white;
    border: 1px solid #ff5252;
}

.stat-card.rejected .stat-number {
    color: white;
}

.stat-card.rejected .stat-label {
    color: rgba(255, 255, 255, 0.9);
}

.approval-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    flex-wrap: wrap;
    gap: 15px;
}

.batch-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.filter-controls input {
    min-width: 250px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.selected-count {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    padding: 5px 10px;
    background: #e9ecef;
    border-radius: 4px;
}

.pending-users-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pending-users-list {
    max-height: 600px;
    overflow-y: auto;
}

.pending-user-card {
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.pending-user-card:hover {
    background-color: #f8f9fa;
}

.pending-user-card:last-child {
    border-bottom: none;
}

.user-card-header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    gap: 15px;
}

.user-selection {
    display: flex;
    align-items: center;
}

.user-checkbox {
    transform: scale(1.2);
    margin: 0;
}

.user-basic-info {
    flex: 1;
}

.user-basic-info .user-name {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.user-basic-info .user-email {
    margin: 0;
    font-size: 14px;
    color: #666;
}

.user-status {
    text-align: right;
}

.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.submit-time {
    display: block;
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

.user-card-body {
    padding: 0 20px 15px 60px;
}

.user-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-item label {
    font-weight: 500;
    color: #555;
    min-width: 80px;
    font-size: 14px;
}

.detail-item span {
    color: #333;
    font-size: 14px;
}

.user-card-actions {
    display: flex;
    gap: 8px;
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #eee;
    justify-content: flex-end;
}

.user-card-actions .btn {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-success {
    background: #28a745;
    color: white;
    border: 1px solid #28a745;
}

.btn-success:hover {
    background: #218838;
    border-color: #1e7e34;
}

.btn-danger {
    background: #dc3545;
    color: white;
    border: 1px solid #dc3545;
}

.btn-danger:hover {
    background: #c82333;
    border-color: #bd2130;
}

.btn-outline {
    background: transparent;
    color: #007bff;
    border: 1px solid #007bff;
}

.btn-outline:hover {
    background: #007bff;
    color: white;
}

.no-pending-users {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-data-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.no-pending-users h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 20px;
}

.no-pending-users p {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 16px;
}

/* 用戶詳情模態框樣式 */
.user-detail-info {
    padding: 20px;
}

.user-detail-info h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 20px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

/* 拒絕原因模態框樣式 */
#reject-reason-modal .form-group {
    margin-bottom: 20px;
}

#reject-reason-modal label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

#reject-reason-modal textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    resize: vertical;
    min-height: 100px;
}

#reject-reason-modal textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 響應式設計 */
@media (max-width: 768px) {
    .approval-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .approval-stats {
        justify-content: space-around;
    }

    .approval-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .batch-actions {
        justify-content: center;
    }

    .filter-controls {
        justify-content: stretch;
    }

    .filter-controls input {
        min-width: auto;
        flex: 1;
    }

    .user-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .user-status {
        text-align: left;
        width: 100%;
    }

    .user-details {
        grid-template-columns: 1fr;
    }

    .user-card-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* 批量操作按鈕樣式 */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn:disabled:hover {
    background: initial;
    border-color: initial;
    color: initial;
}

/* 動畫效果 */
.pending-user-card {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 搜尋高亮效果 */
.user-search-highlight {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 2px;
}

/* ==================== 產品管理樣式 ==================== */

/* 產品管理標題 */
.product-management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
}

.product-management-header .header-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.product-management-header .header-icon {
    font-size: 2.5em;
    opacity: 0.9;
}

.product-management-header h3 {
    margin: 0 0 5px 0;
    font-size: 1.8em;
    font-weight: 600;
}

.product-management-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 1em;
}

/* 產品管理控制項 */
.product-management-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    gap: 20px;
    flex-wrap: wrap;
}

.product-management-controls .search-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex: 1;
}

.product-management-controls .filter-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* 管理員產品表格 */
.admin-products-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 20px;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.admin-table th {
    background: #f8f9fa;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.admin-table td {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

.admin-table tr:hover {
    background-color: #f8f9fa;
}

/* 產品名稱欄位 */
.product-name-cell .product-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.product-name-cell .product-ingredients {
    font-size: 12px;
    color: #666;
    line-height: 1.3;
}

/* 庫存輸入框 */
.stock-input {
    width: 80px;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-size: 14px;
}

.stock-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 管理員操作按鈕 */
.admin-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.admin-actions .btn {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    min-width: auto;
}

.admin-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 管理員分頁 */
.admin-table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 15px 0;
}

.admin-table-pagination .pagination-info {
    color: #666;
    font-size: 14px;
}

.admin-table-pagination .pagination-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.admin-table-pagination .page-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.admin-table-pagination input[type="number"] {
    width: 60px;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

/* 產品編輯模態框 */
.large-modal .modal-content {
    max-width: 800px;
    width: 90%;
}

.form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.form-section h4 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.2em;
    font-weight: 600;
}

.form-section .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.form-section .form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.form-section label {
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.form-section input,
.form-section select,
.form-section textarea {
    padding: 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-section input:focus,
.form-section select:focus,
.form-section textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 功能按鈕樣式 */
.function-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.function-btn.edit-btn {
    background: #28a745;
    color: white;
}

.function-btn.edit-btn:hover {
    background: #218838;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.function-btn.favorite-btn {
    background: #dc3545;
    color: white;
}

.function-btn.favorite-btn:hover {
    background: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* 產品功能欄位 */
.product-function {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .product-management-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .product-management-controls .search-controls,
    .product-management-controls .filter-controls {
        flex-direction: column;
        width: 100%;
    }

    .admin-table {
        font-size: 12px;
    }

    .admin-table th,
    .admin-table td {
        padding: 8px 6px;
    }

    .admin-actions {
        flex-direction: column;
        gap: 4px;
    }

    .form-section .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .admin-table-pagination {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

/* 系統管理分頁字體縮小為 50% */
#admin-tab {
    font-size: 50%;
    /* 將整個管理員區塊字體縮小為原本的一半 */
}

#admin-tab h3,
#admin-tab h4 {
    font-size: 1.2em;
    /* 繼續使用相對單位，會跟隨 50% 縮小 */
}

#admin-tab .admin-nav-tab {
    font-size: 1.5em;
    padding: 8px 16px;
}

#admin-tab .admin-tab-content {
    display: none !important;
    /* 確保未激活的標籤內容隱藏 */
}

#admin-tab .admin-tab-content.active {
    display: block !important;
    /* 確保激活的標籤內容顯示 */
}

#admin-tab .form-control,
#admin-tab input,
#admin-tab textarea,
#admin-tab select {
    font-size: 1em;
    padding: 8px;
}

#admin-tab .btn {
    font-size: 1em;
    padding: 8px 12px;
}

#admin-tab .admin-table {
    font-size: 1em;
}

#admin-tab .admin-table th,
#admin-tab .admin-table td {
    padding: 10px 8px;
}

#admin-tab label {
    font-size: 1em;
    margin-bottom: 6px;
}

/* 確保訊息範本和訊息記錄容器正常顯示 */
#admin-tab .message-history-container,
#admin-tab .templates-container {
    overflow: visible;
    max-height: none;
}

#admin-tab .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* 管理員：留言板統計區縮小為 50% */
#admin-tab .chat-management-header {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 8px;
}

#admin-tab .chat-stats {
    gap: 15px;
}

#admin-tab .stat-item {
    padding: 5px 8px;
}

#admin-tab .chat-management-header .stat-number {
    font-size: 16px !important; /* 原 32px 的 50% */
}

#admin-tab .chat-management-header .stat-label {
    font-size: 16px !important; /* 原 32px 的 50% */
}

/* 管理員：留言板對話區 - 減少中間留白，並放大字體 30% */
#admin-tab .chat-conversation {
    height: 420px; /* 原 600px → 減少留白 */
}

#admin-tab .conversation-messages {
    padding: 10px; /* 原 20px → 減少內距 */
    font-size: 1.3em; /* 文字放大 30% */
}

/* 管理員留言板：訊息泡泡內容字體放大 30% */
#admin-tab .message-bubble,
#admin-tab .message-bubble p,
#admin-tab .message-content,
#admin-tab .message-content p {
    font-size: 1.3em;
}

#admin-tab #admin-reply-input {
    font-size: 1.3em; /* 文字放大 30% */
    padding: 10px 14px; /* 與字級相配的內距 */
}

#admin-tab .reply-input-group {
    gap: 8px; /* 稍微縮小間距 */
}

/* 版權聲明樣式 */
.system-footer {
    margin-top: 40px;
    padding: 20px 0;
    text-align: center;
    border-top: 1px solid #e0e0e0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.system-footer p {
    margin: 0;
    font-size: 12px;
    color: #6c757d;
    font-weight: 400;
    letter-spacing: 0.5px;
}

/* 聯絡功能樣式 */
.contact-header {
    display: none !important;
}

.contact-header h3 {
    margin: 0 0 10px 0;
    font-size: 24px;
    font-weight: 600;
}

.contact-subtitle {
    display: none !important;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 20px;
}

.contact-messages-section,
.contact-form-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.contact-messages-section h4,
.contact-form-section h4 {
    margin: 0 0 20px 0;
    color: #495057;
    font-weight: 600;
    font-size: 18px;
}

.contact-messages-list {
    max-height: 600px;
    overflow-y: auto;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
}

.messages-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.messages-count {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
}

.contact-message-item {
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
}

.contact-message-item:last-child {
    border-bottom: none;
}

/* 管理員回覆樣式 */
.contact-message-content .user-message {
    margin-bottom: 12px;
}

.contact-message-content .admin-reply {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 12px;
    border-radius: 4px;
    margin-top: 12px;
}

.contact-message-content .admin-reply strong {
    color: #007bff;
    display: block;
    margin-bottom: 8px;
}

.contact-message-content .admin-reply p {
    margin: 0 0 8px 0;
    line-height: 1.5;
}

.contact-message-content .reply-time {
    font-size: 0.85em;
    color: #666;
    font-style: italic;
}

/* 對話式聊天樣式 */
.conversation-message {
    margin-bottom: 20px;
    width: 100%;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    font-size: 0.85em;
    color: #666;
}

.admin-header {
    flex-direction: row;
}

.message-bubble {
    display: flex;
    align-items: flex-end;
    max-width: 80%;
    margin-bottom: 10px;
}

/* 用戶訊息（右側，黃色氣泡）*/
.user-message .message-bubble {
    margin-left: auto;
    flex-direction: row-reverse;
}

.user-bubble .message-content {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 18px 18px 5px 18px;
    padding: 12px 16px;
    margin-right: 10px;
    position: relative;
    line-height: 1.4;
}

.user-bubble .message-content::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: -8px;
    width: 0;
    height: 0;
    border: 8px solid transparent;
    border-top-color: #fff3cd;
    border-bottom: 0;
    border-right: 0;
    margin-bottom: -8px;
}

/* 管理員訊息（左側，白色氣泡）*/
.admin-message .message-bubble {
    margin-right: auto;
}

.admin-bubble .message-content {
    background-color: #f8f9fa;
    border: 1px solid #007bff;
    border-radius: 18px 18px 18px 5px;
    padding: 12px 16px;
    margin-left: 10px;
    position: relative;
    line-height: 1.4;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.15);
}

.admin-bubble .message-content::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -8px;
    width: 0;
    height: 0;
    border: 8px solid transparent;
    border-top-color: #f8f9fa;
    border-bottom: 0;
    border-left: 0;
    margin-bottom: -8px;
}

/* 頭像樣式 */
.message-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

.admin-message .message-avatar {
    background-color: #fff3e0;
    border-color: #ff9800;
}

.avatar-icon {
    font-size: 20px;
}

/* 發送者名稱樣式 */
.sender-name {
    font-weight: 600;
    color: #495057;
}

.admin-message .sender-name {
    color: #007bff;
}

.user-message .sender-name {
    color: #28a745;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .message-bubble {
        max-width: 90%;
    }

    .message-header {
        font-size: 0.8em;
    }

    .message-avatar {
        width: 35px;
        height: 35px;
    }

    .avatar-icon {
        font-size: 18px;
    }
}

.contact-message-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.contact-message-subject {
    font-weight: 600;
    color: #495057;
    font-size: 16px;
    margin: 0;
}

.contact-message-time {
    font-size: 12px;
    color: #6c757d;
    white-space: nowrap;
}

.contact-message-content {
    background: #f8f9fa;
    padding: 12px 15px;
    border-radius: 6px;
    color: #333;
    line-height: 1.6;
    white-space: pre-wrap;
    margin-bottom: 10px;
}

.contact-message-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-replied {
    background: #d4edda;
    color: #155724;
}

.contact-form {
    background: white;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.contact-form .form-group {
    margin-bottom: 20px;
}

.contact-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-family: inherit;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.contact-form textarea {
    resize: vertical;
    line-height: 1.5;
}

.contact-form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.contact-form-actions button {
    min-width: 120px;
}

.no-messages {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px 20px;
    font-size: 16px;
}

/* 響應式設計 - 手機版 */
@media (max-width: 768px) {
    .system-footer {
        margin-top: 30px;
        padding: 15px 0;
    }

    .system-footer p {
        font-size: 11px;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .contact-form-actions {
        flex-direction: column;
        gap: 10px;
    }

    .contact-form-actions button {
        width: 100%;
    }
}

/* 
聊天功能樣式 */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 500px;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    background: #f8f9fa;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: linear-gradient(to bottom, #f8f9fa, #ffffff);
}

.message-content {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    animation: fadeInUp 0.3s ease;
}

.message-content.user {
    flex-direction: row-reverse;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin: 0 10px;
    flex-shrink: 0;
}

.message-bubble {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
    word-wrap: break-word;
}

.message-bubble.system {
    background: #e3f2fd;
    color: #1565c0;
    border-bottom-left-radius: 6px;
}

.message-bubble.user {
    background: transparent !important;
    color: #333 !important;
    border-bottom-right-radius: 6px;
}

.message-bubble.admin {
    background: transparent !important;
    color: #333 !important;
    border-bottom-left-radius: 6px;
}

.message-bubble p {
    margin: 0;
    line-height: 1.4;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    display: block;
    margin-top: 4px;
}

.system-message .avatar {
    background: #e3f2fd;
}

.user-message .avatar {
    background: transparent !important;
    color: #666 !important;
}

.admin-message .avatar {
    background: transparent !important;
    color: #666 !important;
}

.chat-input-container {
    padding: 15px 20px;
    background: white;
    border-top: 1px solid #e0e0e0;
}

.chat-form {
    display: flex;
    align-items: flex-end;
}

.input-group {
    display: flex;
    width: 100%;
    gap: 10px;
    align-items: flex-end;
}

#chat-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 20px;
    resize: none;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    max-height: 100px;
    min-height: 44px;
    transition: border-color 0.3s ease;
}

#chat-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.send-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.send-btn:hover {
    background: #5a67d8;
    transform: scale(1.05);
}

.send-btn:active {
    transform: scale(0.95);
}

.send-btn span:first-child {
    display: none;
}

.send-icon {
    font-size: 16px;
}

/* 打字動畫 */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #f0f0f0;
    border-radius: 18px;
    border-bottom-left-radius: 6px;
    max-width: 70px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #999;
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typingDot {

    0%,
    80%,
    100% {
        transform: scale(0.8);
        opacity: 0.5;
    }

    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 響應式設計 */
@media (max-width: 768px) {
    .chat-container {
        height: 400px;
    }

    .message-bubble {
        max-width: 85%;
    }

    .chat-messages {
        padding: 15px;
    }

    .chat-input-container {
        padding: 12px 15px;
    }
}

/* 留言板管理樣式 */
.chat-management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
}

.chat-stats {
    display: flex;
    gap: 30px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 32px !important;
    /* 設定為32px */
    font-weight: bold;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 32px !important;
    /* 標籤也設定為32px */
    opacity: 0.9;
}

.chat-management-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.chat-management-controls .search-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

#chat-search {
    font-size: 15px !important;
    /* 變大50% (10px變成15px) */
    padding: 6px 9px !important;
    width: 225px !important;
    /* 寬度也變大50% */
    height: 45px !important;
    /* 高度也變大50% */
}

#chat-search-btn {
    font-size: 15px !important;
    /* 變大50% (10px變成15px) */
    padding: 6px 12px !important;
    height: 45px !important;
    /* 高度也變大50% */
    white-space: nowrap;
}

.filter-tabs {
    display: flex;
    gap: 10px;
}

.filter-tab {
    padding: 8px 16px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.filter-tab:hover {
    background: #e9ecef;
}

.filter-tab.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.chat-list-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    max-height: 600px;
    overflow-y: auto;
}

.chat-list {
    padding: 0;
}

.chat-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.chat-item:hover {
    background: #f8f9fa;
}

.chat-item.unread {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
}

.chat-item.unread:hover {
    background: #ffeaa7;
}

.chat-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-right: 15px;
    flex-shrink: 0;
}

.chat-info {
    flex: 1;
    min-width: 0;
}

.chat-user-name {
    font-weight: bold;
    margin-bottom: 4px;
    color: #333;
    font-size: 0.3em;
    /* 變成原來的30% */
}

.chat-last-message {
    color: #666;
    font-size: 28px;
    /* 加大100% (原來14px變成28px) */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-meta {
    text-align: right;
    flex-shrink: 0;
    margin-left: 15px;
}

.chat-time {
    font-size: 12px;
    color: #999;
    margin-bottom: 4px;
}

.chat-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: bold;
}

.chat-status.pending {
    background: #ff4444 !important;
    color: white !important;
}

.chat-status.replied {
    background: #28a745;
    color: white;
}

.no-chats {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.no-chats-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

/* 對話視窗樣式 */
.chat-conversation {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: 600px;
    display: flex;
    flex-direction: column;
}

.conversation-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.user-info h4 {
    margin: 0;
    color: white;
    font-weight: bold;
}

.user-info span {
    font-size: 14px;
    color: #f0f0f0;
}

.conversation-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: linear-gradient(to bottom, #f8f9fa, #ffffff);
}

.conversation-reply {
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
    background: white;
}

.reply-input-group {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

#admin-reply-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 20px;
    resize: none;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    transition: border-color 0.3s ease;
}

#admin-reply-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.reply-input-group .btn {
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.reply-input-group .btn span:first-child {
    display: none;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .chat-management-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .chat-management-controls {
        flex-direction: column;
        gap: 15px;
    }

    .chat-stats {
        justify-content: center;
    }

    .conversation-header {
        flex-wrap: wrap;
        gap: 10px;
    }
}

/* 
對話組樣式 - 一組問答一個分隔線 */
.conversation-group {
    margin-bottom: 25px;
    padding: 15px;
    background: transparent;
    border-radius: 0;
    border-bottom: 2px solid #dee2e6;
    position: relative;
}

.conversation-group:last-child {
    border-bottom: none;
}

/* 對話組之間的分隔線 */
.conversation-group::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 25%;
    right: 25%;
    height: 1px;
    background: linear-gradient(to right, transparent, #007bff, transparent);
}

.conversation-group:last-child::after {
    display: none;
}

/* 組內訊息樣式 */
.conversation-group .message-content {
    margin-bottom: 10px;
    background: transparent;
    border-radius: 0;
    padding: 12px;
    box-shadow: none;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.conversation-group .message-content:last-child {
    margin-bottom: 0;
}

/* 用戶訊息（問題） */
.conversation-group .user-message {
    border-left: 3px solid #ffc107;
    flex-direction: row-reverse;
}

/* 管理員訊息（回答） */
.conversation-group .admin-message {
    border-left: 3px solid #28a745;
    margin-left: 15px;
}

/* 訊息頭部 - 名稱和時間在一起 */
.message-header {
    margin-bottom: 8px;
    font-size: 13px;
}

.name-time {
    font-weight: bold;
    color: #333;
}

.user-message .message-header {
    text-align: right;
}

.admin-message .message-header {
    text-align: left;
}

/* 訊息包裝器 */
.message-wrapper {
    max-width: 70%;
    flex: 1;
}

/* 訊息氣泡 */
.message-bubble {
    padding: 0;
    background: transparent;
    border: none;
    border-radius: 0;
}

.message-bubble p {
    margin: 0;
    line-height: 1.4;
}

/* 頭像 */
.avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

/* 強
制移除所有聊天訊息背景色 - 最高優先級 */
.conversation-group,
.conversation-group *,
.message-content,
.message-content *,
.message-bubble,
.message-bubble *,
.user-message,
.user-message *,
.admin-message,
.admin-message *,
.message-bubble.user,
.message-bubble.admin,
.message-bubble.system {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* 確保文字可見且藥局名稱不消失 */
.message-bubble p,
.message-content p,
.conversation-group p,
.name-time,
.message-header,
.message-header *,
.avatar {
    color: #333 !important;
    background: transparent !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 特別保護藥局名稱和時間顯示 */
.name-time {
    color: #666 !important;
    font-weight: normal !important;
    display: inline !important;
}

/* 保持基本結構和分隔線 */
.conversation-group {
    padding: 10px 0 !important;
    margin-bottom: 15px !important;
    border-bottom: 1px solid #e0e0e0 !important;
    background: transparent !important;
}

.conversation-group:last-child {
    border-bottom: none !important;
}

/* 可愛的聊
天框樣式 */
.message-bubble {
    background: #f8f9fa !important;
    border: 2px solid #e9ecef !important;
    border-radius: 18px !important;
    padding: 12px 16px !important;
    margin: 8px 0 !important;
    position: relative !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
}

/* 用戶訊息 - 右側黃色可愛框 */
.message-bubble.user {
    background: #fff3cd !important;
    border: 2px solid #ffeaa7 !important;
    border-radius: 18px !important;
    margin-left: 20% !important;
    position: relative !important;
}

/* 用戶訊息的小尾巴 */
.message-bubble.user::after {
    content: '' !important;
    position: absolute !important;
    right: -10px !important;
    top: 15px !important;
    width: 0 !important;
    height: 0 !important;
    border: 8px solid transparent !important;
    border-left: 8px solid #ffeaa7 !important;
}

/* 管理員訊息 - 左側白色可愛框 */
.message-bubble.admin {
    background: #ffffff !important;
    border: 2px solid #dee2e6 !important;
    border-radius: 18px !important;
    margin-right: 20% !important;
    position: relative !important;
}

/* 管理員訊息的小尾巴 */
.message-bubble.admin::after {
    content: '' !important;
    position: absolute !important;
    left: -10px !important;
    top: 15px !important;
    width: 0 !important;
    height: 0 !important;
    border: 8px solid transparent !important;
    border-right: 8px solid #dee2e6 !important;
}

/* 可愛的頭像樣式 */
.avatar {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    background: linear-gradient(135deg, #74b9ff, #0984e3) !important;
    color: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 18px !important;
    margin: 0 10px !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
    border: 2px solid white !important;
}

/* 用戶頭像 */
.user-message .avatar {
    background: linear-gradient(135deg, #fdcb6e, #e17055) !important;
}

/* 管理員頭像 */
.admin-message .avatar {
    background: linear-gradient(135deg, #74b9ff, #0984e3) !important;
}

/* 訊息容器的可愛樣式 */
.conversation-group {
    background: transparent !important;
    padding: 15px 0 !important;
    margin-bottom: 20px !important;
    border-bottom: 1px dashed #e9ecef !important;
    position: relative !important;
}

/* 最後一個對話組不要底線 */
.conversation-group:last-child {
    border-bottom: none !important;
}

/* 可愛的時間和名稱樣式 */
.name-time {
    color: #6c757d !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    margin-bottom: 5px !important;
    display: block !important;
}

/* 訊息文字樣式 */
.message-bubble p {
    color: #495057 !important;
    margin: 0 !important;
    line-height: 1.5 !important;
    font-size: 14px !important;
}

/* 懸停效果 */
.message-bubble:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 訊息包裝器 */
.message-wrapper {
    display: flex !important;
    flex-direction: column !important;
    flex: 1 !important;
}

/* 訊息內容區域 */
.message-content {
    display: flex !important;
    align-items: flex-start !important;
    gap: 10px !important;
    background: transparent !important;
    padding: 0 !important;
}

/* 用戶訊息靠右 */
.user-message .message-content {
    flex-direction: row-reverse !important;
}

/* 管理員訊息靠左 */
.admin-message .message-content {
    flex-direction: row !important;
}

/* 強化尾巴樣
式 - 最高優先級 */
.message-bubble.user::before {
    content: '' !important;
    position: absolute !important;
    right: -12px !important;
    top: 20px !important;
    width: 0 !important;
    height: 0 !important;
    border: 10px solid transparent !important;
    border-left: 10px solid #fff3cd !important;
    z-index: 2 !important;
}

.message-bubble.user::after {
    content: '' !important;
    position: absolute !important;
    right: -14px !important;
    top: 18px !important;
    width: 0 !important;
    height: 0 !important;
    border: 12px solid transparent !important;
    border-left: 12px solid #ffeaa7 !important;
    z-index: 1 !important;
}

.message-bubble.admin::before {
    content: '' !important;
    position: absolute !important;
    left: -12px !important;
    top: 20px !important;
    width: 0 !important;
    height: 0 !important;
    border: 10px solid transparent !important;
    border-right: 10px solid #ffffff !important;
    z-index: 2 !important;
}

.message-bubble.admin::after {
    content: '' !important;
    position: absolute !important;
    left: -14px !important;
    top: 18px !important;
    width: 0 !important;
    height: 0 !important;
    border: 12px solid transparent !important;
    border-right: 12px solid #dee2e6 !important;
    z-index: 1 !important;
}

/* 確保訊息框有足夠的空間顯示尾巴 */
.user-message {
    margin-right: 20px !important;
}

.admin-message {
    margin-left: 20px !important;
}

/* 確保訊息框的定位正確 */
.message-bubble {
    position: relative !important;
    overflow: visible !important;
    z-index: 3 !important;
}

/* 我的最愛頁面
樣式 */
.favorites-container {
    margin-top: 20px;
}

.favorites-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.favorites-header p {
    margin: 0;
    color: #666;
    font-size: 16px;
}

/* 愛心按鈕樣式 */
.action-btn.favorite {
    background: #f8f9fa !important;
    color: #ccc !important;
    transition: all 0.3s ease !important;
}

.action-btn.favorite:hover {
    background: #ffe6e6 !important;
    color: #ff6b6b !important;
    transform: scale(1.1) !important;
}

.action-btn.favorite.active {
    background: #ffe6e6 !important;
    color: #e74c3c !important;
    animation: heartbeat 1.5s ease-in-out infinite !important;
}

/* 愛心跳動動畫 */
@keyframes heartbeat {
    0% {
        transform: scale(1);
    }

    14% {
        transform: scale(1.1);
    }

    28% {
        transform: scale(1);
    }

    42% {
        transform: scale(1.1);
    }

    70% {
        transform: scale(1);
    }
}

/* 我的最愛標籤樣式 */
.nav-tab[data-tab="favorites"] {
    color: #e74c3c !important;
}

.nav-tab[data-tab="favorites"]:hover {
    color: #c0392b !important;
}

.nav-tab[data-tab="favorites"].active {
    color: #e74c3c !important;
    border-bottom-color: #e74c3c !important;
}

/* 我的最愛
頁面特殊佈局 */
#favorites-tab .products-grid-header {
    grid-template-columns: 2fr 3fr 2fr 1.5fr 2fr 1fr !important;
}

#favorites-tab .product-card {
    grid-template-columns: 2fr 3fr 2fr 1.5fr 2fr 1fr !important;
}

/* 我的最愛頁面不顯示數量欄位 */
#favorites-tab .product-quantity {
    display: none !important;
}

/* 調整我的最愛頁面的功能按鈕 */
#favorites-tab .product-actions {
    gap: 8px !important;
    justify-content: center !important;
}

#favorites-tab .action-btn {
    font-size: 16px !important;
    padding: 8px 12px !important;
}
/*
 極小螢幕優化 (手機直向) */
@media (max-width: 480px) {
    body {
        font-size: 18px; /* 在小螢幕上稍微縮小字體 */
    }
    
    .container {
        padding: 5px;
        max-width: 100%;
    }
    
    .nav-tab {
        min-width: 70px;
        font-size: 14px;
        padding: 6px 2px;
    }
    
    .form-row {
        gap: 10px;
    }
    
    .btn {
        padding: 8px 12px;
        font-size: 14px;
    }
    
    .header-logo {
        height: 40px; /* 縮小 logo */
    }
    
    h1 {
        font-size: 20px; /* 縮小主標題 */
    }
    
    .subtitle {
        font-size: 14px; /* 縮小副標題 */
    }
    
    /* 卡片式佈局改善 */
    .card {
        margin-bottom: 10px;
        padding: 15px;
    }
    
    /* 表格在極小螢幕上的處理 */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}

/* 超小螢幕優化 (舊手機) */
@media (max-width: 360px) {
    body {
        font-size: 16px;
    }
    
    .nav-tab {
        min-width: 60px;
        font-size: 12px;
        padding: 4px 1px;
    }
    
    .btn {
        padding: 6px 8px;
        font-size: 12px;
    }
    
    h1 {
        font-size: 18px;
    }
    
    .container {
        padding: 2px;
    }
}